import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import AffiliateRankForm from '../components/forms/AffiliateRankForm';
import AffiliateRankEditForm from '../components/forms/AffiliateRankEditForm';
import DeleteAffiliateRankModal from '../components/modals/DeleteAffiliateRankModal';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useAffiliateRankData } from '../hooks/useAffiliateData';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import {
  AffiliateRankDto,
  AffiliateRankQueryDto,
  CreateAffiliateRankDto,
  UpdateAffiliateRankDto,
} from '../types/api.types';
import DeleteConfirmModal from '@/shared/components/common/DeleteConfirmModal';

/**
 * Trang quản lý cấp bậc affiliate sử dụng các hooks tối ưu
 */
const AffiliateRankListPageOptimized: React.FC = () => {
  const { t } = useTranslation(['affiliate', 'common']);

  // State cho form và modal
  const [selectedRank, setSelectedRank] = useState<AffiliateRankDto | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (rank: AffiliateRankDto) => {
      setSelectedRank(rank);
      showEditForm();
    },
    [showEditForm]
  );

  // Xử lý xóa
  const handleDelete = useCallback((rank: AffiliateRankDto) => {
    setSelectedRank(rank);
    setIsDeleteModalOpen(true);
  }, []);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<AffiliateRankDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'rankName',
        title: t('affiliate:rank.table.name'),
        dataIndex: 'rankName',
        width: '15%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('affiliate:rank.table.description'),
        dataIndex: 'description',
        width: '25%',
        sortable: true,
      },
      {
        key: 'minCondition',
        title: t('affiliate:rank.table.minReferrals'),
        dataIndex: 'minCondition',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{value as number}</div>;
        },
      },
      {
        key: 'commission',
        title: t('affiliate:rank.table.commissionRate'),
        dataIndex: 'commission',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          return <div className="text-center">{value as number}%</div>;
        },
      },
      {
        key: 'isActive',
        title: t('affiliate:rank.table.status'),
        dataIndex: 'isActive',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const isActive = value as boolean;
          return (
            <div
              className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
                isActive
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              }`}
            >
              {isActive ? t('common:active') : t('common:inactive')}
            </div>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions'), // Sử dụng bản dịch đa ngôn ngữ cho "actions"
        width: '15%',
        render: (_: unknown, record: AffiliateRankDto) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye',
              onClick: () => console.log('View', record.id),
            },
            {
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => handleEdit(record),
            },
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => handleDelete(record),
            },
            // Có thể thêm các action khác vào đây
            // {
            //   id: 'duplicate',
            //   label: t('common:duplicate'),
            //   icon: 'copy',
            //   onClick: () => console.log('Duplicate', record.id),
            // },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')} // Thêm fallback text
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal" // Sử dụng icon ellipsis ngang
                showAllInMenu={true} // Hiển thị tất cả action trong menu
                preferRight={true} // Ưu tiên hiển thị menu bên phải nếu không đủ không gian bên trái
                preferTop={true} // Ưu tiên hiển thị menu bên trên nếu không đủ không gian bên dưới
              />
            </div>
          );
        },
      },
    ],
    [t, handleEdit, handleDelete]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: true },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: false,
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AffiliateRankQueryDto => {
    const queryParams: AffiliateRankQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || '',
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.isActive = params.filterValue as boolean;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AffiliateRankDto, AffiliateRankQueryDto>({
      columns,
      filterOptions,
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Lấy hook từ useAffiliateRankData
  const { useRanks, useCreateRank, useUpdateRank } = useAffiliateRankData();

  // Mutation để tạo rank mới
  const { mutateAsync: createRank, isPending: isCreating } = useCreateRank();

  // Mutation để cập nhật rank
  const { mutateAsync: updateRank, isPending: isUpdating } = useUpdateRank();

  // Gọi API lấy danh sách rank với queryParams từ dataTable
  const { data: rankData, isLoading } = useRanks(dataTable.queryParams);

  // Xử lý thêm mới
  const handleAdd = () => {
    showAddForm();
  };

  // Xử lý submit form thêm mới
  const handleSubmit = async (values: Record<string, unknown>, file?: File) => {
    try {
      // Chuyển đổi values thành CreateAffiliateRankDto
      const createRankDto: CreateAffiliateRankDto = {
        rankName: values['rankName'] as string,
        description: (values['description'] as string) || '',
        commission: values['commission'] as number,
        minCondition: values['minCondition'] as number,
        maxCondition: values['maxCondition'] as number,
        isActive: values['isActive'] as boolean,
        displayOrder: 0, // Giá trị mặc định
      };

      // Gọi API tạo rank mới
      const response = await createRank(createRankDto);

      // Nếu có file và có uploadUrl, thực hiện upload ảnh
      if (file && response.uploadUrl) {
        // Trong thực tế, bạn sẽ sử dụng useFileUpload hook để upload file
        console.log('Uploading file to:', response.uploadUrl);
        // Ví dụ:
        // await uploadToUrl({
        //   file,
        //   presignedUrl: response.uploadUrl,
        // });
      }

      // Đóng form sau khi tạo thành công
      hideAddForm();

      // Hiển thị thông báo thành công
      console.log('Tạo rank thành công:', response);
    } catch (error) {
      console.error('Lỗi khi tạo rank:', error);
    }
  };

  // Xử lý submit form chỉnh sửa
  const handleUpdateSubmit = async (id: number, data: UpdateAffiliateRankDto, file?: File) => {
    try {
      // Gọi API cập nhật rank
      const response = await updateRank({ id, data });

      // Nếu có file và có uploadUrl, thực hiện upload ảnh
      if (file && response.uploadUrl) {
        console.log('Uploading file to:', response.uploadUrl);
      }

      // Đóng form sau khi cập nhật thành công
      hideEditForm();

      // Trì hoãn việc set selectedRank về null để đợi hiệu ứng đóng form hoàn tất
      setTimeout(() => {
        setSelectedRank(null);
      }, 300); // 300ms là thời gian đủ cho hiệu ứng đóng form

      // Hiển thị thông báo thành công
      console.log('Cập nhật rank thành công:', response);
    } catch (error) {
      console.error('Lỗi khi cập nhật rank:', error);
    }
  };

  // Xử lý xóa thành công
  const handleDeleteSuccess = () => {
    console.log('Xóa rank thành công');
    setSelectedRank(null);
  };

  // Xử lý hủy form thêm mới
  const handleCancel = () => {
    hideAddForm();
  };

  // Xử lý hủy form chỉnh sửa
  const handleEditCancel = () => {
    hideEditForm();
    // Trì hoãn việc set selectedRank về null để đợi hiệu ứng đóng form hoàn tất
    setTimeout(() => {
      setSelectedRank(null);
    }, 300); // 300ms là thời gian đủ cho hiệu ứng đóng form
  };

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      true: t('common:active'),
      false: t('common:inactive'),
    },
    t,
  });

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <AffiliateRankForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isCreating}
        />
      </SlideInForm>

      {/* Form chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {selectedRank && (
          <AffiliateRankEditForm
            rank={selectedRank}
            onSubmit={handleUpdateSubmit}
            onCancel={handleEditCancel}
            isSubmitting={isUpdating}
          />
        )}
      </SlideInForm>

      {/* Modal xác nhận xóa */}
      {selectedRank && (
        <DeleteConfirmModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={handleDelete}
          title={t('common:confirmDelete')}
          message={t('affiliate:rank.confirmDeleteMessage')}
          itemName={selectedRank.rankName}
          isSubmitting={isDeleting}
        />
      )}

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={rankData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: rankData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: rankData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default AffiliateRankListPageOptimized;
