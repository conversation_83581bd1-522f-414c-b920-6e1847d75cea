import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { FieldValues } from 'react-hook-form';
import {
  Card,
  Table,
  ActionMenu,
  ActionMenuItem,
  Loading,
  ConfirmDeleteModal,
} from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useFormErrors } from '@/shared/hooks/';
import { useDataTable } from '@/shared/hooks/table';
import CustomerForm from '../components/forms/CustomerForm';
import CustomerDetailView from '../components/forms/CustomerDetailView';
import CustomerImport from '../components/import/CustomerImport';
import { useConvertCustomers, useBulkDeleteConvertCustomers } from '../hooks/useCustomerQuery';
import {
  UserConvertCustomerListItemDto,
  QueryUserConvertCustomerDto,
  UserConvertCustomerSortField,
} from '../types/customer.types';

/**
 * Trang quản lý khách hàng sử dụng các hooks tối ưu
 */
const CustomerPage: React.FC = () => {
  const { t } = useTranslation(['business', 'common']);

  // State cho form và modal
  const [selectedCustomer, setSelectedCustomer] = useState<UserConvertCustomerListItemDto | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);

  // Hook để xử lý lỗi form
  const { formRef, setFormErrors } = useFormErrors<FieldValues>();

  // Hook bulk delete
  const bulkDeleteMutation = useBulkDeleteConvertCustomers();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();

  // Sử dụng hook animation cho detail view
  const { isVisible: isDetailVisible, showForm: showDetail, hideForm: hideDetail } = useSlideForm();

  // Sử dụng hook animation cho import modal
  const { isVisible: isImportVisible, showForm: showImport, hideForm: hideImport } = useSlideForm();

  // Xử lý xem chi tiết khách hàng
  const handleViewCustomer = useCallback((customer: UserConvertCustomerListItemDto) => {
    setSelectedCustomer(customer);
    showDetail();
  }, [showDetail]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<UserConvertCustomerListItemDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '5%', sortable: true },
      {
        key: 'avatar',
        title: t('business:customer.form.avatar'),
        dataIndex: 'avatar',
        width: '8%',
        render: (value: unknown) => {
          const avatarUrl = value as string;
          return avatarUrl ? (
            <img src={avatarUrl} alt="Avatar" className="w-8 h-8 rounded-full object-cover" />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-500 text-xs">N/A</span>
            </div>
          );
        },
      },
      {
        key: 'name',
        title: t('business:common.form.name'),
        dataIndex: 'name',
        width: '15%',
        sortable: true,
        render: (value: unknown) => (value as string) || 'N/A',
      },
      {
        key: 'email',
        title: t('business:common.form.email'),
        dataIndex: 'email',
        width: '20%',
        sortable: true,
        render: (value: unknown) => {
          const email = value as { primary?: string } | string | null;
          if (typeof email === 'object' && email !== null) {
            return email.primary || 'N/A';
          }
          return email || 'N/A';
        },
      },
      {
        key: 'phone',
        title: t('business:common.form.phone'),
        dataIndex: 'phone',
        width: '15%',
        sortable: true,
        render: (value: unknown) => (value as string) || 'N/A',
      },
      {
        key: 'platform',
        title: t('business:customer.platform'),
        dataIndex: 'platform',
        width: '12%',
        sortable: true,
        render: (value: unknown) => (value as string) || 'N/A',
      },
      {
        key: 'createdAt',
        title: t('common:createdAt'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          try {
            if (!value) return 'N/A';

            let date: Date;

            if (typeof value === 'number') {
              date = new Date(value);
            } else if (typeof value === 'string') {
              if (/^\d+$/.test(value)) {
                date = new Date(Number(value));
              } else {
                date = new Date(value);
              }
            } else if (value instanceof Date) {
              date = value;
            } else {
              return 'N/A';
            }

            if (isNaN(date.getTime())) {
              return 'N/A';
            }

            return date.toLocaleDateString('vi-VN');
          } catch (error) {
            console.error('Error formatting date:', error, 'Value:', value);
            return 'N/A';
          }
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '10%',
        render: (_: unknown, record: UserConvertCustomerListItemDto) => {
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye',
              onClick: () => handleViewCustomer(record),
            },
          ];

          return (
            <div className="flex justify-center">
              <ActionMenu
                items={actionItems}
                menuTooltip={t('common:moreActions', 'Thêm hành động')}
                iconSize="sm"
                iconVariant="default"
                placement="bottom"
                menuWidth="180px"
                menuIcon="more-horizontal"
                showAllInMenu={true}
                preferRight={true}
                preferTop={true}
              />
            </div>
          );
        },
      },
    ],
    [t, handleViewCustomer]
  );

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'facebook', label: 'Facebook', icon: 'facebook', value: 'facebook' },
      { id: 'zalo', label: 'Zalo', icon: 'message-circle', value: 'zalo' },
      { id: 'website', label: 'Website', icon: 'globe', value: 'website' },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): QueryUserConvertCustomerDto => {
    const queryParams: QueryUserConvertCustomerDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy as UserConvertCustomerSortField || UserConvertCustomerSortField.CREATED_AT,
      sortDirection: params.sortDirection || SortDirection.DESC,
    };

    if (params.filterValue !== 'all') {
      queryParams.platform = params.filterValue as string;
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable
  const dataTable = useDataTable<UserConvertCustomerListItemDto, QueryUserConvertCustomerDto>({
    columns,
    tableDataOptions: {
      defaultPageSize: 10,
      defaultSortBy: UserConvertCustomerSortField.CREATED_AT,
      defaultSortDirection: SortDirection.DESC,
    },
    filterOptions: {
      options: filterOptions,
      defaultSelectedId: 'all',
    },
    dateRangeOptions: {
      defaultDateRange: [null, null],
    },
    createQueryParams,
  });

  // Gọi API lấy danh sách customer với queryParams từ dataTable
  const { data: customerData, isLoading, error, refetch } = useConvertCustomers(dataTable.queryParams);

  // Cập nhật dataTable khi có dữ liệu mới
  useEffect(() => {
    dataTable.updateTableData(customerData, isLoading);
  }, [customerData, isLoading, dataTable]);

  // Xử lý thêm mới
  const handleAdd = () => {
    // Clear form errors khi mở form mới
    setFormErrors({});
    showAddForm();
  };

  // Xử lý khi tạo khách hàng thành công
  const handleCustomerSuccess = () => {
    // Clear form errors và đóng form khi thành công
    setFormErrors({});
    hideAddForm();
  };

  // Xử lý hủy form thêm khách hàng
  const handleCancelAdd = () => {
    // Clear form errors khi hủy form
    setFormErrors({});
    hideAddForm();
  };

  // Xử lý đóng detail view
  const handleCloseDetail = () => {
    setSelectedCustomer(null);
    hideDetail();
  };

  // Xử lý import khách hàng
  const handleImport = () => {
    showImport();
  };

  // TaskQueue sẽ tự động refresh data khi import hoàn thành

  // Xử lý hiển thị modal xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = () => {
    setShowBulkDeleteModal(true);
  };

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = () => {
    setShowBulkDeleteModal(false);
  };

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = async () => {
    try {
      const customerIds = selectedRowKeys.map(key => Number(key));
      await bulkDeleteMutation.mutateAsync(customerIds);
      setSelectedRowKeys([]);
      setShowBulkDeleteModal(false);
    } catch (error) {
      console.error('Lỗi khi xóa nhiều khách hàng:', error);
    }
  };

  // Xử lý thay đổi selection
  const handleSelectionChange = (selectedKeys: React.Key[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // Hiển thị loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading />
      </div>
    );
  }

  // Hiển thị error state
  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-4">Có lỗi xảy ra khi tải dữ liệu</p>
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteModal}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete')}
        message={t('business:customer.bulkDeleteConfirmation', {
          count: selectedRowKeys.length,
        })}
        itemName={`${selectedRowKeys.length} khách hàng`}
        isSubmitting={bulkDeleteMutation.isPending}
      />

      <MenuIconBar
        onSearch={dataTable.tableData.setSearchTerm}
        onAdd={handleAdd}
        additionalIcons={[
          {
            tooltip: t('business:customer.import.title'),
            icon: 'upload',
            variant: 'primary',
            onClick: handleImport,
          },
          {
            icon: 'refresh',
            tooltip: 'Làm mới dữ liệu',
            variant: 'primary',
            onClick: () => refetch(),
          },
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'ghost',
            onClick: handleShowBulkDeleteConfirm,
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Active Filters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={() => dataTable.tableData.setSearchTerm('')}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={dataTable.filter.selectedId}
        onClearFilter={() => dataTable.filter.setSelectedId('all')}
        onClearAll={() => {
          dataTable.tableData.setSearchTerm('');
          dataTable.filter.setSelectedId('all');
          dataTable.dateRange.handleClearDateRange();
        }}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={dataTable.dateRange.handleClearDateRange}
      />

      {/* Form thêm khách hàng */}
      <SlideInForm isVisible={isAddFormVisible}>
        <CustomerForm
          formRef={formRef}
          onSuccess={handleCustomerSuccess}
          onCancel={handleCancelAdd}
          title={t('business:customer.addForm')}
        />
      </SlideInForm>

      {/* Detail view khách hàng */}
      <SlideInForm isVisible={isDetailVisible}>
        {selectedCustomer && (
          <CustomerDetailView customerId={selectedCustomer.id} onClose={handleCloseDetail} />
        )}
      </SlideInForm>

      {/* Import form */}
      <SlideInForm isVisible={isImportVisible}>
        <CustomerImport onClose={hideImport} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={customerData?.items || []}
          rowKey="id"
          rowSelection={{
            selectedRowKeys,
            onChange: handleSelectionChange,
          }}
          sortable
          pagination={{
            current: dataTable.tableData.currentPage,
            pageSize: dataTable.tableData.pageSize,
            total: customerData?.meta?.totalItems || 0,
            showSizeChanger: true,
            onChange: (page: number, size?: number) => {
              dataTable.tableData.setCurrentPage(page);
              if (size && size !== dataTable.tableData.pageSize) {
                dataTable.tableData.setPageSize(size);
              }
            },
          }}
          onSortChange={dataTable.tableData.handleSortChange}
        />
      </Card>
    </div>
  );
};

export default CustomerPage;
